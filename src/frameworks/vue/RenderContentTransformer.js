const gogocode = require('gogocode');
const chalk = require('chalk');
const { AIService } = require('../../ai/AiService');

/**
 * RenderContent 转换器
 * 专门处理 Vue 2 到 Vue 3 的 renderContent 方法转换
 * 将 JSX 语法转换为 h() 函数调用
 */
class RenderContentTransformer extends AIService {

  constructor(options = {}) {
    super(options);
    this.stats = {
      total: 0,
      astSuccess: 0,
      aiSuccess: 0,
      failed: 0,
      skipped: 0
    };
    this.ast = null; // 存储传入的 AST
  }

  /**
   * 转换代码中的 renderContent 方法
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径（用于调试）
   * @param {Object} ast - 可选的 AST 对象
   * @returns {string} 转换后的代码
   */
  async transform(code, filePath = '', ast = null) {
    try {
      this.ast = ast;

      if (!this.hasRenderContent(code)) {
        return code;
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`🔄 检测到 renderContent，开始转换: ${filePath}`));
      }

      const astResult = await this.transformWithAST(code, filePath);
      if (astResult.success) {
        this.stats.astSuccess++;
        if (this.options.verbose) {
          console.log(chalk.green(`✅ AST 转换成功: ${filePath}`));
        }
        return astResult.code;
      }

      if (this.enabled) {
        const aiResult = await this.transformWithAI(code, filePath);
        if (aiResult.success) {
          this.stats.aiSuccess++;
          if (this.options.verbose) {
            console.log(chalk.green(`🤖 AI 转换成功: ${filePath}`));
          }
          return aiResult.code;
        }
      }

      this.stats.failed++;
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️ renderContent 转换失败: ${filePath}`));
      }
      return code;
    } catch (error) {
      this.stats.failed++;
      if (this.options.verbose) {
        console.error(chalk.red(`❌ renderContent 转换异常: ${filePath}`), error.message);
      }
      return code;
    } finally {
      this.stats.total++;
    }
  }

  /**
   * 检查代码是否包含 renderContent 方法
   * @param {string} code - 源代码
   * @returns {boolean} 是否包含 renderContent 方法
   */
  hasRenderContent(code) {
    // 匹配多种 renderContent 方法定义形式：
    // 1. renderContent(h, { node, data }) {
    // 2. renderContent: function(h, { node, data }) {
    // 3. "renderContent": function(h, { node, data }) {
    const patterns = [
      /renderContent\s*\(\s*h\s*,\s*\{\s*node\s*,\s*data\s*\}\s*\)\s*\{/,
      /renderContent\s*:\s*function\s*\(\s*h\s*,\s*\{\s*node\s*,\s*data\s*\}\s*\)\s*\{/,
      /['"]\s*renderContent\s*['"]\s*:\s*function\s*\(\s*h\s*,\s*\{\s*node\s*,\s*data\s*\}\s*\)\s*\{/
    ];
    
    return patterns.some(pattern => pattern.test(code));
  }

  async transformWithAST(code, filePath) {
    try {
      // 如果有传入的 AST，使用它；否则解析代码
      let ast = this.ast;
      if (!ast) {
        ast = gogocode(code);
      }

      let hasChanges = false;
      const transformedAst = ast;

      // 尝试多种搜索模式来查找 renderContent 方法
      const searchPatterns = [
        'renderContent(h, { node, data }) { $$$ }',
        'renderContent($_, { node, data }) { $$$ }',
        'renderContent: function(h, { node, data }) { $$$ }',
        'renderContent: function($_, { node, data }) { $$$ }'
      ];

      let foundMethods = [];
      for (const pattern of searchPatterns) {
        try {
          const methods = transformedAst.find(pattern);
          if (methods && methods.length > 0) {
            foundMethods = [];
            methods.each(method => {
              foundMethods.push(method);
            });
            if (this.options.verbose) {
              console.log(chalk.gray(`  找到 renderContent 方法，使用模式: ${pattern}`));
            }
            break;
          }
        } catch (patternError) {
          // 忽略模式错误，尝试下一个
        }
      }

      // 如果没有找到，尝试更通用的搜索
      if (foundMethods.length === 0) {
        try {
          // 查找所有方法定义
          const allMethods = transformedAst.find('{ renderContent: function($$$) { $$$ } }');
          if (allMethods && allMethods.length > 0) {
            allMethods.each(methodNode => {
              const renderContentProperty = methodNode.find('renderContent: function($$$) { $$$ }');
              if (renderContentProperty && renderContentProperty.length > 0) {
                renderContentProperty.each(prop => {
                  foundMethods.push(prop);
                });
              }
            });
          }

          // 尝试查找函数表达式
          if (foundMethods.length === 0) {
            const funcExpressions = transformedAst.find('renderContent($$$) { $$$ }');
            if (funcExpressions && funcExpressions.length > 0) {
              funcExpressions.each(funcNode => {
                const params = funcNode.attr('params');
                if (params && params.length >= 2) {
                  const firstParam = params[0];
                  const secondParam = params[1];

                  // 检查参数模式是否匹配 renderContent
                  if (firstParam && firstParam.type === 'Identifier' && firstParam.name === 'h' &&
                      secondParam && secondParam.type === 'ObjectPattern') {
                    foundMethods.push(funcNode);
                  }
                }
              });
            }
          }
        } catch (genericError) {
          if (this.options.verbose) {
            console.warn(chalk.yellow(`⚠️ 通用搜索失败: ${genericError.message}`));
          }
        }
      }

      if (foundMethods.length > 0) {
        foundMethods.forEach(node => {
          try {
            const params = node.attr('params');
            if (params && params.length >= 2) {
              // 转换方法签名：移除第一个参数 h
              const newParams = params.slice(1); // 移除第一个参数 h
              node.attr('params', newParams);

              // 转换方法体中的 JSX
              this.transformJSXInAST(node);

              hasChanges = true;

              if (this.options.verbose) {
                console.log(chalk.gray('  转换 renderContent 方法签名'));
              }
            }
          } catch (error) {
            if (this.options.verbose) {
              console.warn(chalk.yellow(`⚠️ 转换单个 renderContent 方法失败: ${error.message}`));
            }
          }
        });
      }

      if (hasChanges) {
        const transformedCode = transformedAst.generate();
        const finalCode = this.ensureHImport(transformedCode);

        return {
          success: true,
          code: finalCode
        };
      }

      return { success: false, error: 'No renderContent methods found or transformed' };
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ AST 转换失败，回退到正则方式: ${error.message}`));
      }

      return { success: false, error: error.message };
    }
  }

  transformJSXInAST(methodNode) {
    try {
      // 查找返回语句中的 JSX 元素
      const returnStatements = methodNode.find('return $$$');
      if (returnStatements && returnStatements.length > 0) {
        returnStatements.each(returnStmt => {
          const returnArg = returnStmt.attr('argument');
          if (returnArg && returnArg.type === 'JSXElement') {
            try {
              const transformedH = this.convertJSXToHCode(returnArg);
              if (transformedH) {
                // 直接替换返回语句的参数
                const newReturnCode = `return ${transformedH}`;
                const newReturnAST = gogocode(newReturnCode);
                const newReturnStmt = newReturnAST.find('return $$$');
                if (newReturnStmt && newReturnStmt.length > 0) {
                  returnStmt.replaceWith(newReturnStmt.eq(0));
                }
              }
            } catch (jsxError) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 转换返回语句中的 JSX 失败: ${jsxError.message}`));
              }
            }
          }
        });
      }

      // 也尝试直接查找 JSX 元素（作为备用）
      const jsxElements = methodNode.find('JSXElement');
      if (jsxElements && jsxElements.length > 0) {
        jsxElements.each(jsxNode => {
          try {
            const transformedH = this.convertJSXToHCode(jsxNode.node || jsxNode);
            if (transformedH) {
              const hAST = gogocode(transformedH);
              const callExpression = hAST.find('CallExpression')[0];
              if (callExpression) {
                jsxNode.replaceWith(callExpression);
              }
            }
          } catch (jsxError) {
            if (this.options.verbose) {
              console.warn(chalk.yellow(`⚠️ 转换单个 JSX 元素失败: ${jsxError.message}`));
            }
          }
        });
      }

      // 处理 JSX 片段
      const jsxFragments = methodNode.find('JSXFragment');
      if (jsxFragments && jsxFragments.length > 0) {
        jsxFragments.each(fragmentNode => {
          try {
            const transformedH = this.convertJSXFragmentToHCode(fragmentNode.node || fragmentNode);
            if (transformedH) {
              const hAST = gogocode(transformedH);
              const expression = hAST.find('Expression')[0] || hAST.find('ArrayExpression')[0];
              if (expression) {
                fragmentNode.replaceWith(expression);
              }
            }
          } catch (fragmentError) {
            if (this.options.verbose) {
              console.warn(chalk.yellow(`⚠️ 转换单个 JSX 片段失败: ${fragmentError.message}`));
            }
          }
        });
      }
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX AST 转换失败: ${error.message}`));
      }
    }
  }

  convertJSXToHCode(jsxNode) {
    try {
      const tagName = jsxNode.openingElement?.name?.name;
      if (!tagName) {
        return null;
      }

      const props = this.extractJSXPropsFromNode(jsxNode);
      const children = this.extractJSXChildrenFromNode(jsxNode);

      // 构建 h() 函数调用
      let hCall = `h('${tagName}'`;

      if (props && Object.keys(props).length > 0) {
        hCall += `, ${JSON.stringify(props)}`;
      } else {
        hCall += ', null';
      }

      if (children.length > 0) {
        if (children.length === 1) {
          hCall += `, ${children[0]}`;
        } else {
          hCall += `, [${children.join(', ')}]`;
        }
      }

      hCall += ')';
      return hCall;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 转 h() 代码失败: ${error.message}`));
      }
      return null;
    }
  }

  convertJSXFragmentToHCode(fragmentNode) {
    try {
      const children = this.extractJSXChildrenFromNode(fragmentNode);

      if (children.length === 0) {
        return null;
      }

      if (children.length === 1) {
        return children[0];
      }

      // 多个子元素使用数组包装
      return `[${children.join(', ')}]`;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 片段转换失败: ${error.message}`));
      }
      return null;
    }
  }

  extractJSXPropsFromNode(jsxNode) {
    const props = {};

    try {
      const attributes = jsxNode.openingElement?.attributes || [];

      attributes.forEach(attr => {
        if (attr.type === 'JSXAttribute') {
          const name = attr.name?.name;
          if (!name) return;

          const value = attr.value;

          if (value) {
            if (value.type === 'Literal') {
              props[name] = value.value;
            } else if (value.type === 'JSXExpressionContainer') {
              const expression = value.expression;
              if (expression.type === 'Literal') {
                props[name] = expression.value;
              } else if (expression.type === 'ObjectExpression') {
                const objProps = {};
                if (expression.properties) {
                  expression.properties.forEach(prop => {
                    if (prop.type === 'Property' && prop.key && prop.value) {
                      const key = prop.key.name || prop.key.value;
                      const val = prop.value.value;
                      if (key && val !== undefined) {
                        objProps[key] = val;
                      }
                    }
                  });
                }
                props[name] = objProps;
              } else {
                // 对于其他表达式，尝试转换为字符串
                props[name] = `{${this.generateExpressionCode(expression)}}`;
              }
            }
          } else {
            // 布尔属性
            props[name] = true;
          }
        }
      });
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 提取 JSX 属性失败: ${error.message}`));
      }
    }

    return props;
  }

  extractJSXChildrenFromNode(jsxNode) {
    const children = [];

    try {
      const jsxChildren = jsxNode.children || [];

      jsxChildren.forEach(child => {
        if (child.type === 'JSXText') {
          const text = child.value ? child.value.trim() : '';
          if (text) {
            children.push(`'${text.replace(/'/g, "\\'")}'`);
          }
        } else if (child.type === 'JSXExpressionContainer') {
          const expression = child.expression;
          if (expression.type === 'Identifier') {
            children.push(expression.name);
          } else if (expression.type === 'Literal') {
            children.push(JSON.stringify(expression.value));
          } else {
            children.push(this.generateExpressionCode(expression));
          }
        } else if (child.type === 'JSXElement') {
          const nestedH = this.convertJSXToHCode(child);
          if (nestedH) {
            children.push(nestedH);
          }
        }
      });
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 提取 JSX 子元素失败: ${error.message}`));
      }
    }

    return children;
  }

  generateExpressionCode(expression) {
    try {
      if (expression.type === 'Identifier') {
        return expression.name;
      } else if (expression.type === 'MemberExpression') {
        const object = this.generateExpressionCode(expression.object);
        const property = expression.computed
          ? `[${this.generateExpressionCode(expression.property)}]`
          : `.${expression.property.name}`;
        return `${object}${property}`;
      } else if (expression.type === 'Literal') {
        return JSON.stringify(expression.value);
      } else {
        // 为其他类型返回一个占位符
        return `/* ${expression.type} */`;
      }
    } catch (error) {
      return `/* error: ${error.message} */`;
    }
  }

  convertJSXToH(jsxNode) {
    try {
      const openingElement = jsxNode.attr('openingElement');
      if (!openingElement) {
        return null;
      }

      const tagName = openingElement.name ? openingElement.name.name : null;
      if (!tagName) {
        return null;
      }

      const props = this.extractJSXProps(jsxNode);
      const children = this.extractJSXChildren(jsxNode);

      // 构建 h() 函数调用
      let hCall = `h('${tagName}'`;

      if (props && Object.keys(props).length > 0) {
        hCall += `, ${JSON.stringify(props)}`;
      } else {
        hCall += ', null';
      }

      if (children.length > 0) {
        if (children.length === 1) {
          hCall += `, ${children[0]}`;
        } else {
          hCall += `, [${children.join(', ')}]`;
        }
      }

      hCall += ')';

      // 创建新的 AST 节点并返回
      const hAST = gogocode(hCall, { isProgram: false });
      const callExpression = hAST.find('CallExpression');
      if (callExpression && callExpression.length > 0) {
        return callExpression.eq(0);
      }

      return null;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 转 h() 失败: ${error.message}`));
      }
      return null;
    }
  }

  extractJSXProps(jsxNode) {
    const props = {};

    try {
      const openingElement = jsxNode.attr('openingElement');
      if (!openingElement) {
        return props;
      }

      const attributes = openingElement.attributes || [];

      attributes.forEach(attr => {
        if (attr.type === 'JSXAttribute') {
          const name = attr.name ? attr.name.name : null;
          if (!name) return;

          const value = attr.value;

          if (value) {
            if (value.type === 'Literal') {
              props[name] = value.value;
            } else if (value.type === 'JSXExpressionContainer') {
              // 处理 JSX 表达式
              const expression = value.expression;
              if (expression.type === 'Literal') {
                props[name] = expression.value;
              } else if (expression.type === 'ObjectExpression') {
                // 处理对象表达式（如 style）
                const objProps = {};
                if (expression.properties) {
                  expression.properties.forEach(prop => {
                    if (prop.type === 'Property' && prop.key && prop.value) {
                      const key = prop.key.name || prop.key.value;
                      const val = prop.value.value;
                      if (key && val !== undefined) {
                        objProps[key] = val;
                      }
                    }
                  });
                }
                props[name] = objProps;
              } else {
                // 对于其他表达式，尝试转换为字符串
                try {
                  const expressionCode = gogocode(expression).generate();
                  props[name] = expressionCode;
                } catch (e) {
                  // 如果无法转换，跳过此属性
                  if (this.options.verbose) {
                    console.warn(chalk.yellow(`⚠️ 无法转换 JSX 表达式: ${e.message}`));
                  }
                }
              }
            }
          } else {
            // 布尔属性
            props[name] = true;
          }
        }
      });
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 提取 JSX 属性失败: ${error.message}`));
      }
    }

    return props;
  }

  extractJSXChildren(jsxNode) {
    const children = [];

    try {
      const jsxChildren = jsxNode.attr('children') || [];

      jsxChildren.forEach(child => {
        if (child.type === 'JSXText') {
          const text = child.value ? child.value.trim() : '';
          if (text) {
            children.push(`'${text.replace(/'/g, "\\'")}'`);
          }
        } else if (child.type === 'JSXExpressionContainer') {
          // 处理 JSX 表达式
          const expression = child.expression;
          if (expression.type === 'Identifier') {
            children.push(expression.name);
          } else if (expression.type === 'MemberExpression') {
            try {
              const memberCode = gogocode(expression).generate();
              children.push(memberCode);
            } catch (e) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 无法生成成员表达式代码: ${e.message}`));
              }
            }
          } else if (expression.type === 'Literal') {
            children.push(JSON.stringify(expression.value));
          } else {
            // 对于其他表达式，尝试生成代码
            try {
              const expressionCode = gogocode(expression).generate();
              children.push(expressionCode);
            } catch (e) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 无法生成表达式代码: ${e.message}`));
              }
            }
          }
        } else if (child.type === 'JSXElement') {
          const nestedH = this.convertJSXToH(child);
          if (nestedH) {
            try {
              const nestedCode = nestedH.generate();
              children.push(nestedCode);
            } catch (e) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 无法生成嵌套 JSX 代码: ${e.message}`));
              }
            }
          }
        }
      });
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 提取 JSX 子元素失败: ${error.message}`));
      }
    }

    return children;
  }

  convertJSXFragmentToH(fragmentNode) {
    try {
      const children = this.extractJSXChildren(fragmentNode);

      if (children.length === 0) {
        return null;
      }

      if (children.length === 1) {
        const singleChild = gogocode(children[0], { isProgram: false });
        const expression = singleChild.find('Expression');
        if (expression && expression.length > 0) {
          return expression.eq(0);
        }
        return singleChild;
      }

      // 多个子元素使用数组包装
      const arrayCode = `[${children.join(', ')}]`;
      const arrayAST = gogocode(arrayCode, { isProgram: false });
      const arrayExpression = arrayAST.find('ArrayExpression');
      if (arrayExpression && arrayExpression.length > 0) {
        return arrayExpression.eq(0);
      }
      return arrayAST;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 片段转换失败: ${error.message}`));
      }
      return null;
    }
  }

  parseInlineStyle(styleStr) {
    const styleObj = {};
    const declarations = styleStr.split(';').filter(d => d.trim());

    declarations.forEach(decl => {
      const [property, value] = decl.split(':').map(s => s.trim());
      if (property && value) {
        // 转换 CSS 属性名为 camelCase
        const camelProperty = property.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());

        // 处理数值
        if (/^\d+$/.test(value)) {
          styleObj[camelProperty] = parseInt(value);
        } else {
          styleObj[camelProperty] = value;
        }
      }
    });

    return styleObj;
  }

  ensureHImport(code) {
    if (code.includes('import { h }') || code.includes('import {h}')) {
      return code;
    }

    // 查找现有的 vue 导入
    const vueImportMatch = code.match(/import\s+{([^}]+)}\s+from\s+['"]vue['"]/);
    if (vueImportMatch) {
      const existingImports = vueImportMatch[1].trim();
      const newImports = existingImports.includes('h') ? existingImports : `h, ${existingImports}`;
      return code.replace(vueImportMatch[0], `import { ${newImports} } from 'vue'`);
    }

    // 查找 script 标签开始位置
    const scriptMatch = code.match(/(<script[^>]*>)/);
    if (scriptMatch) {
      const insertPos = scriptMatch.index + scriptMatch[0].length;
      return code.slice(0, insertPos) + '\nimport { h } from \'vue\'\n' + code.slice(insertPos);
    }

    // 如果没有找到合适的位置，在文件开头添加
    return "import { h } from 'vue'\n" + code;
  }

  async transformWithAI(code, filePath) {
    try {
      const prompt = this.generateRenderContentPrompt(code, filePath);
      const response = await this.callAI(prompt);

      if (response && response.trim().length > 0) {
        // 解析 AI 响应
        const transformedCode = this.parseAIResponse(response, code);
        if (transformedCode) {
          return {
            success: true,
            code: transformedCode
          };
        }
      }

      return { success: false, error: 'AI response invalid' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  generateRenderContentPrompt(code, filePath) {
    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请将以下代码中的 renderContent 方法从 Vue 2 格式转换为 Vue 3 格式。

**转换要求：**
1. 将 renderContent(h, { node, data }) 改为 renderContent({ node, data })
2. 将 JSX 语法转换为 h() 函数调用
3. 确保导入了 h 函数：import { h } from 'vue'
4. 保持原有的逻辑和样式不变

**转换示例：**
Vue 2 格式：
\`\`\`javascript
renderContent(h, { node, data }) {
  return (
    <div class="connect_id_title">
      {data.title}{' '}
      <span class="connect_id_number" style="opacity:0">
        {data.id}
      </span>
    </div>
  )
}
\`\`\`

Vue 3 格式：
\`\`\`javascript
renderContent({ node, data }) {
  return h('div', { class: 'connect_id_title' }, [
    h('span', null, data.title),
    h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)
  ])
}
\`\`\`

**需要转换的代码：**
文件路径：${filePath}

\`\`\`javascript
${code}
\`\`\`

请只返回转换后的完整代码，不要添加额外的解释。`;
  }

  parseAIResponse(response, originalCode) {
    try {
      // 尝试从代码块中提取代码
      const codeBlockMatch = response.match(/```(?:javascript|js)?\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        return codeBlockMatch[1].trim();
      }

      // 如果没有代码块，检查响应是否直接是代码
      if (response.includes('renderContent') && response.includes('h(')) {
        return response.trim();
      }

      return null;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 解析 AI 响应失败: ${error.message}`));
      }
      return null;
    }
  }

  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.total > 0
        ? ((this.stats.astSuccess + this.stats.aiSuccess) / this.stats.total * 100).toFixed(1) + '%'
        : '0%'
    };
  }

}

module.exports = RenderContentTransformer;
