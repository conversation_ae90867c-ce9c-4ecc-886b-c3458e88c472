const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const glob = require('glob')
const gogocode = require('gogocode')
const { transform: vueTransform } = require('gogocode-plugin-vue')
const { transform: elementTransform } = require('@unit-mesh/gogocode-plugin-element')
const MigrationStrategySelector = require('./third-party/MigrationStrategySelector');
const MigrationDocGenerator = require('./third-party/MigrationDocGenerator');
const FailureLogger = require('../../utils/failureLogger');
const VueTransformer = require('./CustomVueTransformer');
const RenderContentTransformer = require('./RenderContentTransformer');
const GogocodeErrorHandler = require('./GogocodeErrorHandler');

/**
 * Vue 代码迁移器
 * 支持从源目录迁移到目标目录，或原地修改
 */
class VueCodeMigrator {
	constructor (inputPath, options = {}) {
		this.inputPath = path.resolve(inputPath)
		this.options = Object.assign({
			srcDir: 'src',
			outputSrcDir: 'src',
			includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
			excludePatterns: ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.*/**'],
			isOutputMode: false,
			outputPath: '',
			aiApiKey: '',
			aiProvider: 'auto',
			buildCommand: 'npm run build',
			verbose: false,
			enableMigrationStrategy: true
		}, options)
		this.isOutputMode = this.options.isOutputMode
		this.outputPath = this.options.outputPath
		this.projectPath = this.inputPath
		this.stats = {
			success: 0,
			failed: 0,
			skipped: 0,
			copied: 0,
			total: 0,
			failedFiles: []
		}
		this.failureLogger = new FailureLogger(this.inputPath);

		// 迁移策略相关
		this.migrationStrategy = null
		this.analysisResult = null

		// 初始化转换器
		this.vueTransformer = new VueTransformer({
			verbose: this.options.verbose,
			aiApiKey: this.options.aiApiKey,
			aiProvider: this.options.aiProvider
		});

		// 初始化 RenderContentTransformer
		this.renderContentTransformer = new RenderContentTransformer({
			verbose: this.options.verbose,
			aiApiKey: this.options.aiApiKey,
			aiProvider: this.options.aiProvider
		});

		// 初始化错误处理器
		this.errorHandler = new GogocodeErrorHandler({
			verbose: this.options.verbose,
			maxRetries: 3,
			fallbackToOriginal: true
		});
	}

	/**
	 * 执行迁移
	 */
	async migrate () {
		console.log(chalk.blue('🔄 开始 Vue 代码迁移 (原地修改模式)...'))

		try {
			await this.ensureOutputDirectory()

			const files = await this.getFilesToMigrate()
			console.log(chalk.gray(`找到 ${files.length} 个文件需要迁移`))

			this.stats.total = files.length

			await this.failureLogger.initialize();

			if (this.options.enableMigrationStrategy) {
				await this.performMigrationStrategyAnalysis()
			}

			await this.performGogocodeTransformation(files)

			await this.cleanupGogocodeTransferFiles()

			await this.copyGogocodeTransferFile()

			if (this.failureLogger) {
				await this.failureLogger.saveFailures();
			}

			this.printMigrationStats()

			if (this.getTransformErrorStats().total > 0 || this.stats.failed > 0) {
				await this.saveErrorReport()
			}

			return this.stats

		} catch (error) {
			console.error(chalk.red('❌ 迁移失败:'), error.message)
			throw error
		}
	}

	/**
	 * 使用 Gogocode 批量转换文件
	 */
	async performGogocodeTransformation(files) {
		console.log(chalk.gray(`开始使用 Gogocode 转换 ${files.length} 个文件...`));
		for (const filePath of files) {
			await this.migrateFile(filePath)
		}

		console.log(chalk.green(`✅ Gogocode 转换完成: 成功 ${this.stats.success}, 失败 ${this.stats.failed}`));

		if (this.stats.failed > 0) {
			console.log(chalk.yellow(`⚠️  发现 ${this.stats.failed} 个转换失败的文件，将在后续步骤中使用 AI 修复`));
		}
	}

	/**
	 * 执行迁移策略分析（仅针对失败文件）
	 */
	async performMigrationStrategyAnalysis() {
		try {
			// 只有在有失败文件时才进行分析
			if (this.stats.failedFiles.length === 0) {
				console.log(chalk.gray('🎉 没有失败的文件!'));
				return;
			}

			console.log(chalk.blue(`🎯 分析 ${this.stats.failedFiles.length} 个失败文件...`));

			// 只有在有失败文件且有 AI API Key 时才进行 AI 分析
			if (!this.options.aiApiKey) {
				console.log(chalk.yellow('⚠️  未提供 AI API Key，跳过 AI 分析'));
				return;
			}

			const strategySelector = new MigrationStrategySelector(this.inputPath, {
				aiApiKey: this.options.aiApiKey,
				verbose: this.options.verbose,
				failedFiles: this.stats.failedFiles // 传递失败文件信息
			});

			const strategyResult = await strategySelector.selectStrategy();
			this.migrationStrategy = strategyResult.strategy;
			this.analysisResult = strategyResult.analysisResult;

			// 根据策略执行相应的操作
			if (this.migrationStrategy === 'ai-assisted') {
				console.log(chalk.green('🤖 将使用 AI 辅助修复失败文件'));
				// AI 辅助策略专门针对失败文件
				const migrationPlan = await strategySelector.executeAIAssistedStrategy();
				this.migrationPlan = migrationPlan;
			} else if (this.migrationStrategy === 'documentation-guided') {
				console.log(chalk.yellow('📖 将使用文档指导修复失败文件'));

				// 生成迁移指导文档
				if (this.options.generateMigrationDoc) {
					const docGenerator = new MigrationDocGenerator(this.inputPath, this.analysisResult, {
						outputPath: path.join(this.inputPath, 'migration-guide.md')
					});
					await docGenerator.generateMigrationGuide();
				}

				const migrationGuide = await strategySelector.executeDocumentationGuidedStrategy();
				this.migrationGuide = migrationGuide;
			}

			console.log(chalk.green('✅ 失败文件分析完成'));
		} catch (error) {
			console.warn(chalk.yellow(`⚠️  失败文件分析失败: ${error.message}`));
			console.log(chalk.gray('将跳过 AI 修复步骤'));
		}
	}

	/**
	 * 确保输出目录存在
	 */
	async ensureOutputDirectory () {
		if (this.isOutputMode) {
			const outputSrcPath = path.join(this.outputPath, this.options.outputSrcDir)
			await fs.ensureDir(outputSrcPath)
			console.log(chalk.gray(`已创建输出目录: ${outputSrcPath}`))
		}
	}

	/**
	 * 复制 gogocodeTransfer.js 工具文件到目标项目
	 *
	 * - '@/utils/gogocodeTransfer'
	 */
	async copyGogocodeTransferFile () {
		try {
			// gogocodeTransfer.js 源文件路径（在迁移工具中）
			const sourceTransferFile = path.join(__dirname, 'utils', 'gogocodeTransfer.js')

			// 目标路径
			const targetPath = this.isOutputMode ? this.outputPath : this.inputPath
			const targetUtilsDir = path.join(targetPath, this.options.outputSrcDir || this.options.srcDir, 'utils')
			const targetTransferFile = path.join(targetUtilsDir, 'gogocodeTransfer.js')

			// 检查源文件是否存在
			if (await fs.pathExists(sourceTransferFile)) {
				// 确保目标 utils 目录存在
				await fs.ensureDir(targetUtilsDir)

				// 复制文件
				await fs.copy(sourceTransferFile, targetTransferFile)
				console.log(chalk.green(`✅ 已复制 gogocodeTransfer.js 到: ${path.relative(targetPath, targetTransferFile)}`))
			} else {
				console.log(chalk.yellow(`⚠️  未找到 gogocodeTransfer.js 源文件: ${sourceTransferFile}`))
			}
		} catch (error) {
			console.log(chalk.yellow(`⚠️  复制 gogocodeTransfer.js 失败: ${error.message}`))
		}
	}

	/**
	 * 获取需要迁移的文件列表
	 */
	async getFilesToMigrate () {
		const srcPath = path.join(this.inputPath, this.options.srcDir)
		const files = []

		// 检查源目录是否存在
		if (!await fs.pathExists(srcPath)) {
			throw new Error(`源目录不存在: ${srcPath}`)
		}

		for (const pattern of this.options.includePatterns) {
			const matchedFiles = glob.sync(pattern, {
				cwd: srcPath,
				ignore: this.options.excludePatterns,
				absolute: false
			})

			files.push(...matchedFiles.map(file => path.join(srcPath, file)))
		}

		// 去重
		return [...new Set(files)]
	}

	/**
	 * 迁移单个文件
	 */
	async migrateFile (filePath) {
		try {
			const relativePath = path.relative(this.inputPath, filePath)
			process.stdout.write(chalk.gray(`迁移: ${relativePath} ... `))

			// 读取文件内容
			const source = await fs.readFile(filePath, 'utf8')

			// 根据文件类型选择迁移策略
			let transformedCode
			const ext = path.extname(filePath)

			if (ext === '.vue' || ext === '.js' ) {
				transformedCode = await this.migrateVueFile(source, filePath)
			} else if (ext === '.ts') {
				transformedCode = await this.migrateJsFile(source, filePath)
			} else {
				console.log(chalk.yellow('跳过'))
				this.stats.skipped++
				await this.failureLogger.logSkipped(filePath, `不支持的文件类型: ${ext}`)
				return
			}

			const outputFilePath = this.getOutputFilePath(filePath)
			if (transformedCode && (transformedCode !== source || this.isOutputMode)) {
				await fs.ensureDir(path.dirname(outputFilePath))
				await fs.writeFile(outputFilePath, transformedCode, 'utf8')

				if (this.isOutputMode && outputFilePath !== filePath) {
					console.log(chalk.green('✅ (复制+转换)'))
					this.stats.copied++
					await this.failureLogger.logSuccess(filePath, {
						type: 'copy_and_transform',
						outputPath: outputFilePath,
						hasChanges: true
					})
				} else {
					console.log(chalk.green('✅'))
				}
				this.stats.success++
				await this.failureLogger.logSuccess(filePath, {
					type: 'transform',
					hasChanges: true,
					originalSize: source.length,
					transformedSize: transformedCode.length
				})
			} else {
				if (this.isOutputMode) {
					// 输出模式下，即使没有变化也要复制文件
					await fs.ensureDir(path.dirname(outputFilePath))
					await fs.copy(filePath, outputFilePath)
					console.log(chalk.gray('复制（无变化）'))
					this.stats.copied++
					await this.failureLogger.logSuccess(filePath, {
						type: 'copy_only',
						outputPath: outputFilePath,
						hasChanges: false
					})
				} else {
					console.log(chalk.gray('跳过（无变化）'))
				}
				this.stats.skipped++
				await this.failureLogger.logSkipped(filePath, '文件内容无变化')
			}

		} catch (error) {
			console.log(chalk.red('❌'))
			this.stats.failed++

			// 确保错误信息是字符串
			const errorMessage = typeof error === 'string' ? error :
								(error && error.message) ? error.message :
								(error && typeof error === 'object') ? JSON.stringify(error) :
								String(error);

			// 记录详细的失败信息，包括绝对路径
			const failureInfo = {
				file: path.relative(this.inputPath, filePath),
				absolutePath: filePath,
				error: errorMessage,
				errorType: this.categorizeError(errorMessage),
				timestamp: new Date().toISOString()
			}

			this.stats.failedFiles.push(failureInfo)

			// 记录失败到 FailureLogger
			await this.failureLogger.logFailure(filePath, errorMessage, {
				fileType: path.extname(filePath),
				errorType: this.categorizeError(errorMessage)
			})

			// 如果是 Gogocode 相关错误，记录更多信息
			if (errorMessage.includes('gogocode') || errorMessage.includes('eventsApi')) {
				console.log(chalk.yellow(`    Gogocode 转换错误: ${failureInfo.file}`))
			}

			// 添加调试信息
			if (this.options.verbose) {
				console.log(chalk.gray(`    错误类型: ${failureInfo.errorType}`))
				console.log(chalk.gray(`    错误详情: ${errorMessage.substring(0, 100)}...`))
			}
		}
	}

	/**
	 * 获取输出文件路径
	 */
	getOutputFilePath (inputFilePath) {
		if (!this.isOutputMode) {
			return inputFilePath
		}

		// 计算相对于输入源目录的路径
		const inputSrcPath = path.join(this.inputPath, this.options.srcDir)
		const relativeToSrc = path.relative(inputSrcPath, inputFilePath)

		// 构建输出路径
		const outputSrcPath = path.join(this.outputPath, this.options.outputSrcDir)
		return path.join(outputSrcPath, relativeToSrc)
	}

	safeGogocodeTransform(source, filePath, transformName = 'unknown') {
		return this.errorHandler.safeCreateAST(source, filePath)
	}

	enhancedVueTransform(source, filePath) {
		// 使用错误处理器进行安全转换
		return this.errorHandler.safeVueTransform(
			vueTransform,
			{
				path: filePath,
				source: source,
			},
			{
				gogocode: gogocode,
			},
			{
				rootPath: path.resolve(this.projectPath, this.options.srcDir),
				outFilePath: filePath,
				outRootPath: path.dirname(filePath),
			}
		)
	}

	enhancedElementTransform(source, filePath) {
		return this.errorHandler.safeVueTransform(
			elementTransform,
			{
				path: filePath,
				source: source,
			},
			{
				gogocode: gogocode,
			},
			{
				rootPath: path.resolve(this.projectPath, this.options.srcDir),
				outFilePath: filePath,
				outRootPath: path.dirname(filePath),
			}
		)
	}

	async migrateVueFile (source, filePath) {
		try {
			let transformedCode = source
			const relativePath = path.relative(this.projectPath, filePath)

			const transformSteps = []

			try {
				const vueTransformed = this.enhancedVueTransform(source, filePath)
				if (vueTransformed !== source) {
					transformedCode = vueTransformed
					transformSteps.push('Vue转换')
				}
			} catch (vueError) {
				console.warn(chalk.yellow(`Vue 转换异常: ${vueError.message}`))
				this.recordTransformError(filePath, 'vue-transform', vueError.message)
			}

			// 2. 增强的 Element 转换
			try {
				const elementTransformed = this.enhancedElementTransform(transformedCode, filePath)
				if (elementTransformed !== transformedCode) {
					transformedCode = elementTransformed
					transformSteps.push('Element转换')
				}
			} catch (elementError) {
				console.warn(chalk.yellow(`Element 转换异常: ${elementError.message}`))
				this.recordTransformError(filePath, 'element-transform', elementError.message)
			}

			// 3. 应用自定义转换规则
			try {
				const customTransformed = await this.vueTransformer.transform(transformedCode, filePath)
				if (customTransformed !== transformedCode) {
					transformedCode = customTransformed
					transformSteps.push('自定义转换')
				}
			} catch (customError) {
				console.warn(chalk.yellow(`自定义转换异常: ${customError.message}`))
				this.recordTransformError(filePath, 'custom-transform', customError.message)
			}

			// 4. 应用 RenderContent 转换（使用 AST）
			try {
				// 为 RenderContentTransformer 提供 AST
				const ast = this.safeGogocodeTransform(transformedCode, filePath, 'renderContent');
				if (ast) {
					const renderContentTransformed = await this.renderContentTransformer.transform(transformedCode, filePath, ast);
					if (renderContentTransformed !== transformedCode) {
						transformedCode = renderContentTransformed;
						transformSteps.push('RenderContent转换');
					}
				} else {
					// 如果 AST 解析失败，尝试不使用 AST
					const renderContentTransformed = await this.renderContentTransformer.transform(transformedCode, filePath);
					if (renderContentTransformed !== transformedCode) {
						transformedCode = renderContentTransformed;
						transformSteps.push('RenderContent转换');
					}
				}
			} catch (renderContentError) {
				console.warn(chalk.yellow(`RenderContent转换异常: ${renderContentError.message}`))
				this.recordTransformError(filePath, 'renderContent-transform', renderContentError.message)
			}

			// 5. 最终验证转换结果
			const finalValidation = this.validateTransformResult(source, transformedCode, filePath)
			if (!finalValidation.isValid) {
				console.warn(chalk.yellow(`⚠️  转换结果验证失败: ${finalValidation.reason}`))
				transformedCode = source // 回退到原始代码
				transformSteps.length = 0 // 清空转换步骤
			}

			// 检查是否为关键文件且转换失败
			const isKeyFile = this.isKeyFile(filePath)
			const hasChanges = transformedCode !== source

			// 记录转换结果
			if (this.options.verbose && transformSteps.length > 0) {
				console.log(chalk.gray(`  转换步骤: ${transformSteps.join(' → ')}`))
			}

			if (isKeyFile && !hasChanges) {
				// 标记关键文件为需要 AI 修复
				this.markForAIRepair(filePath, '关键文件自动转换失败')
				console.log(chalk.yellow(`⚠️  关键文件转换失败，已标记为 AI 修复: ${relativePath}`))
			}

			// 更新统计信息
			if (hasChanges) {
				this.stats.success++
			} else {
				this.stats.skipped++
			}

			return transformedCode

		} catch (error) {
			console.error(chalk.red(`❌ 迁移文件失败: ${filePath}`), error.message)
			this.stats.failed++
			this.stats.failedFiles.push({
				file: path.relative(this.projectPath, filePath),
				absolutePath: filePath,
				error: error.message
			})
			this.recordTransformError(filePath, 'migration-failed', error.message)
			return source
		}
	}

	/**
	 * 验证转换结果
	 */
	validateTransformResult(originalCode, transformedCode, filePath) {
		try {
			// 基本检查
			if (typeof transformedCode !== 'string') {
				return { isValid: false, reason: '转换结果不是字符串' }
			}

			if (transformedCode.trim().length === 0) {
				return { isValid: false, reason: '转换结果为空' }
			}

			// 语法检查
			if (filePath.endsWith('.vue')) {
				// Vue 文件基本结构检查
				const hasTemplate = transformedCode.includes('<template>')
				const hasScript = transformedCode.includes('<script>')

				if (originalCode.includes('<template>') && !hasTemplate) {
					return { isValid: false, reason: '丢失 template 部分' }
				}

				if (originalCode.includes('<script>') && !hasScript) {
					return { isValid: false, reason: '丢失 script 部分' }
				}
			} else if (filePath.endsWith('.js') || filePath.endsWith('.ts')) {
				// JavaScript/TypeScript 语法检查
				try {
					gogocode(transformedCode)
				} catch (syntaxError) {
					return { isValid: false, reason: `语法错误: ${syntaxError.message}` }
				}
			}

			// 检查是否有明显的转换错误标记
			const errorMarkers = ['undefined', '[object Object]', 'TypeError']
			for (const marker of errorMarkers) {
				if (transformedCode.includes(marker) && !originalCode.includes(marker)) {
					return { isValid: false, reason: `包含错误标记: ${marker}` }
				}
			}
			
			// 特殊处理 null：只有在明显错误的上下文中才视为错误
			if (transformedCode.includes('null') && !originalCode.includes('null')) {
				// 检查是否在有效的上下文中（比如 h() 函数调用）
				const validNullContexts = [
					/h\([^,]+,\s*null/,  // h('div', null, ...)
					/h\([^,]+,\s*null\s*,/,  // h('div', null, ...)
					/=\s*null/,  // variable = null
					/:\s*null/   // property: null
				];
				
				const hasValidNullContext = validNullContexts.some(pattern => pattern.test(transformedCode));
				if (!hasValidNullContext) {
					return { isValid: false, reason: `包含错误标记: null` }
				}
			}

			return { isValid: true, reason: null }
		} catch (error) {
			return { isValid: false, reason: `验证过程出错: ${error.message}` }
		}
	}

	/**
	 * 记录转换错误
	 */
	recordTransformError(filePath, errorType, errorMessage) {
		if (!this.transformErrors) {
			this.transformErrors = []
		}

		this.transformErrors.push({
			file: path.relative(this.projectPath, filePath),
			absolutePath: filePath,
			errorType: errorType,
			errorMessage: errorMessage,
			timestamp: new Date().toISOString()
		})
	}

	/**
	 * 获取转换错误统计
	 */
	getTransformErrorStats() {
		// 合并自定义错误和错误处理器的错误
		const customErrors = this.transformErrors || []
		const handlerErrors = this.errorHandler.getErrorStats()

		const allErrors = [...customErrors, ...handlerErrors.errors]
		const byType = {}

		allErrors.forEach(error => {
			const errorType = error.errorType || 'unknown'
			byType[errorType] = (byType[errorType] || 0) + 1
		})

		return {
			total: allErrors.length,
			byType: byType,
			errors: allErrors
		}
	}

	/**
	 * 检查是否为关键文件
	 */
	isKeyFile (filePath) {
		const keyFiles = [
			'src/main.js',
			'src/main.ts',
			'src/App.vue',
			'src/router/index.js',
			'src/router/index.ts',
			'src/store/index.js',
			'src/store/index.ts'
		]

		const relativePath = path.relative(this.projectPath, filePath)

		// 调试输出
		if (this.options.verbose) {
			console.log(chalk.gray(`检查关键文件: ${relativePath}`))
		}

		return keyFiles.some(keyFile => {
			const isMatch = relativePath === keyFile || relativePath.endsWith(keyFile)
			if (this.options.verbose && isMatch) {
				console.log(chalk.blue(`✅ 识别为关键文件: ${relativePath}`))
			}
			return isMatch
		})
	}

	/**
	 * 标记文件为需要 AI 修复
	 */
	markForAIRepair (filePath, reason) {
		const relativePath = path.relative(this.projectPath, filePath)

		// 添加到需要 AI 修复的文件列表
		if (!this.filesForAIRepair) {
			this.filesForAIRepair = []
		}

		this.filesForAIRepair.push({
			file: relativePath,
			absolutePath: filePath,
			error: reason,
			type: 'key-file-conversion-failed'
		})
	}

	/**
	 * 获取需要 AI 修复的文件列表
	 */
	getFilesForAIRepair () {
		return this.filesForAIRepair || []
	}

	/**
	 * 增强的 JS/TS 文件迁移
	 */
	async migrateJsFile (source, filePath) {
		try {
			let transformedCode = source
			const relativePath = path.relative(this.projectPath, filePath)
			const transformSteps = []

			// 1. 安全的 gogocode AST 转换
			try {
				const ast = this.safeGogocodeTransform(source, filePath, 'JS/TS')
				if (ast) {
					const generated = ast.generate()
					if (generated !== source) {
						transformedCode = generated
						transformSteps.push('AST标准化')
					}
				}
			} catch (gogocodeError) {
				console.warn(chalk.yellow(`Gogocode AST 转换警告: ${gogocodeError.message}`))
				this.recordTransformError(filePath, 'gogocode-ast', gogocodeError.message)
			}

			// 2. Vue 2 到 Vue 3 的特定转换
			try {
				const vueTransformed = this.enhancedVueTransform(transformedCode, filePath)
				if (vueTransformed !== transformedCode) {
					transformedCode = vueTransformed
					transformSteps.push('Vue2→3转换')
				}
			} catch (vueError) {
				console.warn(chalk.yellow(`Vue 转换警告: ${vueError.message}`))
				this.recordTransformError(filePath, 'vue-js-transform', vueError.message)
			}

			// 3. 应用自定义转换规则
			try {
				const customTransformed = await this.vueTransformer.transform(transformedCode, filePath)
				if (customTransformed !== transformedCode) {
					transformedCode = customTransformed
					transformSteps.push('自定义转换')
				}
			} catch (customError) {
				console.warn(chalk.yellow(`自定义转换警告: ${customError.message}`))
				this.recordTransformError(filePath, 'custom-js-transform', customError.message)
			}

			// 4. 应用 RenderContent 转换（使用 AST）
			try {
				// 为 RenderContentTransformer 提供 AST
				const ast = this.safeGogocodeTransform(transformedCode, filePath, 'renderContent');
				if (ast) {
					const renderContentTransformed = await this.renderContentTransformer.transform(transformedCode, filePath, ast);
					if (renderContentTransformed !== transformedCode) {
						transformedCode = renderContentTransformed;
						transformSteps.push('RenderContent转换');
					}
				} else {
					// 如果 AST 解析失败，尝试不使用 AST
					const renderContentTransformed = await this.renderContentTransformer.transform(transformedCode, filePath);
					if (renderContentTransformed !== transformedCode) {
						transformedCode = renderContentTransformed;
						transformSteps.push('RenderContent转换');
					}
				}
			} catch (renderContentError) {
				console.warn(chalk.yellow(`RenderContent转换警告: ${renderContentError.message}`))
				this.recordTransformError(filePath, 'renderContent-js-transform', renderContentError.message)
			}

			// 5. 最终验证
			const finalValidation = this.validateTransformResult(source, transformedCode, filePath)
			if (!finalValidation.isValid) {
				console.warn(chalk.yellow(`⚠️  JS 转换结果验证失败: ${finalValidation.reason}`))
				transformedCode = source
				transformSteps.length = 0
			}

			// 检查是否为关键文件且转换失败
			const isKeyFile = this.isKeyFile(filePath)
			const hasChanges = transformedCode !== source

			// 记录转换结果
			if (this.options.verbose && transformSteps.length > 0) {
				console.log(chalk.gray(`  JS转换步骤: ${transformSteps.join(' → ')}`))
			}

			if (isKeyFile && !hasChanges) {
				this.markForAIRepair(filePath, '关键文件自动转换失败')
				console.log(chalk.yellow(`⚠️  关键文件转换失败，已标记为 AI 修复: ${relativePath}`))
			}

			// 更新统计信息
			if (hasChanges) {
				this.stats.success++
			} else {
				this.stats.skipped++
			}

			return transformedCode
		} catch (error) {
			this.recordTransformError(filePath, 'js-migration-failed', error.message)
			throw new Error(`JS 文件转换失败: ${error.message}`)
		}
	}

	/**
	 * 增强的迁移统计打印
	 */
	printMigrationStats () {
		console.log('\n' + chalk.bold('📊 代码迁移统计:'))
		console.log(`总计: ${this.stats.total} 个文件`)
		console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`))
		console.log(chalk.gray(`⏸️  跳过: ${this.stats.skipped} 个`))
		console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`))

		if (this.isOutputMode) {
			console.log(chalk.blue(`📁 复制: ${this.stats.copied} 个文件`))
			console.log(chalk.gray(`输出路径: ${this.outputPath}`))
		}

		// 显示转换错误统计
		const errorStats = this.getTransformErrorStats()
		if (errorStats.total > 0) {
			console.log(chalk.yellow(`\n⚠️  转换警告: ${errorStats.total} 个`))
			Object.entries(errorStats.byType).forEach(([type, count]) => {
				console.log(chalk.gray(`  ${type}: ${count} 个`))
			})
		}

		// 显示失败的文件
		if (this.stats.failedFiles.length > 0) {
			console.log(chalk.red('\n❌ 失败的文件:'))
			this.stats.failedFiles.forEach(({ file, error }) => {
				console.log(chalk.red(`  ${file}: ${error}`))
			})
		}

		// 显示需要 AI 修复的文件
		const aiRepairFiles = this.getFilesForAIRepair()
		if (aiRepairFiles.length > 0) {
			console.log(chalk.blue(`\n🤖 需要 AI 修复: ${aiRepairFiles.length} 个文件`))
			aiRepairFiles.forEach(({ file, error }) => {
				console.log(chalk.blue(`  ${file}: ${error}`))
			})
		}

		const successRate = ((this.stats.success / this.stats.total) * 100).toFixed(1)
		console.log(chalk.bold(`\n成功率: ${successRate}%`))

		// 提供改进建议
		this.printImprovementSuggestions(errorStats)
	}

	/**
	 * 打印改进建议
	 */
	printImprovementSuggestions(errorStats) {
		if (errorStats.total === 0) return

		console.log(chalk.cyan('\n💡 改进建议:'))

		// 分析常见错误类型并给出建议
		if (errorStats.byType['vue-transform'] > 0) {
			console.log(chalk.cyan('  • Vue 转换错误较多，建议检查 gogocode-plugin-vue 版本兼容性'))
		}

		if (errorStats.byType['gogocode-ast'] > 0) {
			console.log(chalk.cyan('  • AST 解析错误较多，建议检查源代码语法'))
		}

		if (errorStats.byType['element-transform'] > 0) {
			console.log(chalk.cyan('  • Element UI 转换错误，建议手动检查组件使用'))
		}

		if (this.stats.failed > this.stats.success) {
			console.log(chalk.cyan('  • 失败率较高，建议使用 AI 辅助修复模式'))
		}
	}

	/**
	 * 获取失败的文件列表（用于 AI 修复）
	 */
	getFailedFiles() {
		return this.stats.failedFiles
	}

	/**
	 * 获取迁移策略
	 */
	getMigrationStrategy() {
		return this.migrationStrategy
	}

	/**
	 * 获取分析结果
	 */
	getAnalysisResult() {
		return this.analysisResult
	}

	/**
	 * 获取迁移计划（AI 辅助策略）
	 */
	getMigrationPlan() {
		return this.migrationPlan
	}

	/**
	 * 获取迁移指南（文档指导策略）
	 */
	getMigrationGuide() {
		return this.migrationGuide
	}

	/**
	 * 分类错误类型
	 */
	categorizeError (errorMessage) {
		if (errorMessage.includes('eventsApi') || errorMessage.includes('gogocode-plugin-vue')) {
			return 'gogocode-events-api'
		} else if (errorMessage.includes('gogocode')) {
			return 'gogocode-general'
		} else if (errorMessage.includes('SyntaxError')) {
			return 'syntax-error'
		} else if (errorMessage.includes('Cannot read properties')) {
			return 'property-access-error'
		} else {
			return 'unknown'
		}
	}

	/**
	 * 生成详细的错误报告
	 */
	generateErrorReport() {
		const errorStats = this.getTransformErrorStats()
		const aiRepairFiles = this.getFilesForAIRepair()
		const failedFiles = this.getFailedFiles()

		const report = {
			summary: {
				total: this.stats.total,
				success: this.stats.success,
				skipped: this.stats.skipped,
				failed: this.stats.failed,
				successRate: ((this.stats.success / this.stats.total) * 100).toFixed(1)
			},
			transformErrors: errorStats,
			aiRepairNeeded: aiRepairFiles,
			criticalFailures: failedFiles,
			recommendations: this.generateRecommendations(errorStats, aiRepairFiles, failedFiles)
		}

		return report
	}

	/**
	 * 生成修复建议
	 */
	generateRecommendations(errorStats, aiRepairFiles, failedFiles) {
		const recommendations = []

		// 基于错误类型的建议
		if (errorStats.byType['vue-transform'] > 0) {
			recommendations.push({
				type: 'version-compatibility',
				priority: 'high',
				message: '检查 gogocode-plugin-vue 版本兼容性',
				action: '升级或降级 gogocode-plugin-vue 到兼容版本'
			})
		}

		if (errorStats.byType['gogocode-ast'] > 0) {
			recommendations.push({
				type: 'syntax-issues',
				priority: 'medium',
				message: 'AST 解析错误，可能存在语法问题',
				action: '检查源代码语法，修复语法错误'
			})
		}

		if (aiRepairFiles.length > 0) {
			recommendations.push({
				type: 'ai-repair',
				priority: 'high',
				message: `${aiRepairFiles.length} 个关键文件需要 AI 修复`,
				action: '使用 AI 修复功能处理关键文件'
			})
		}

		if (failedFiles.length > this.stats.success) {
			recommendations.push({
				type: 'migration-strategy',
				priority: 'critical',
				message: '失败率过高，建议调整迁移策略',
				action: '考虑使用手动迁移或分步迁移'
			})
		}

		return recommendations
	}

	/**
	 * 保存错误报告到文件
	 */
	async saveErrorReport() {
		try {
			const report = this.generateErrorReport()
			const reportPath = path.join(this.projectPath, 'migration-error-report.json')

			await fs.writeJson(reportPath, report, { spaces: 2 })
			console.log(chalk.blue(`📋 错误报告已保存: ${reportPath}`))

			return reportPath
		} catch (error) {
			console.warn(chalk.yellow(`⚠️  保存错误报告失败: ${error.message}`))
			return null
		}
	}

	/**
	 * 清理多余的 gogocodeTransfer.js 文件
	 */
	async cleanupGogocodeTransferFiles() {
		try {
			console.log(chalk.blue('🧹 清理多余的 gogocodeTransfer.js 文件...'))

			// 查找所有的 gogocodeTransfer.js 文件
			const gogocodeTransferFiles = glob.sync('**/utils/gogocodeTransfer.js', {
				cwd: this.inputPath,
				absolute: true,
				ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
			})

			// 保留主文件路径
			const mainGogocodeTransferFile = path.join(this.inputPath, 'src', 'utils', 'gogocodeTransfer.js')

			let cleanedCount = 0
			for (const filePath of gogocodeTransferFiles) {
				if (filePath !== mainGogocodeTransferFile) {
					try {
						await fs.remove(filePath)
						cleanedCount++

						const utilsDir = path.dirname(filePath)
						const utilsDirContents = await fs.readdir(utilsDir)
						if (utilsDirContents.length === 0) {
							await fs.remove(utilsDir)
						}
					} catch (error) {
						console.warn(chalk.yellow(`⚠️  删除文件失败: ${path.relative(this.inputPath, filePath)}`))
					}
				}
			}

			if (cleanedCount > 0) {
				console.log(chalk.green(`✅ 已清理 ${cleanedCount} 个多余的 gogocodeTransfer.js 文件`))
			} else {
				console.log(chalk.gray('📝 没有发现多余的 gogocodeTransfer.js 文件'))
			}

		} catch (error) {
			console.warn(chalk.yellow(`⚠️  清理 gogocodeTransfer.js 文件时出错: ${error.message}`))
		}
	}
}

module.exports = VueCodeMigrator
