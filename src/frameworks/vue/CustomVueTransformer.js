/**
 * Vue 转换器
 * 提供自定义的 Vue 2 到 Vue 3 代码转换规则
 */
class CustomVueTransformer {
  constructor(options = {}) {
    this.options = options;
    // 注意：RenderContentTransformer 现在在 VueCodeMigrator 中单独管理
  }
  /**
   * 应用自定义 Vue 转换规则
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径（可选）
   * @returns {string} 转换后的代码
   */
  async transform(code, filePath = '') {
    // 统一处理 gogocodeTransfer 导入 - 将所有相对路径替换为全局路径
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理其他可能的 gogocodeTransfer 导入格式
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理更复杂的相对路径
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 替换 Element UI 导入
    code = code.replace(
      /import\s+{\s*([^}]+)\s*}\s+from\s+['"]element-ui['\"]/g,
      (match, imports) => {
        // 清理导入列表中的多余空格
        const cleanImports = imports.replace(/\s+/g, ' ').trim()
        return `import { ${cleanImports} } from 'element-plus'`
      }
    )

    // 替换 Element UI 完整导入
    code = code.replace(
      /import\s+ElementUI\s+from\s+['"]element-ui['\"]/g,
      'import ElementPlus from \'element-plus\''
    )

    // 替换 Element UI CSS 导入
    code = code.replace(
      /import\s+['"]element-ui\/lib\/theme-chalk\/index\.css['\"]/g,
      'import \'element-plus/dist/index.css\''
    )

    // 替换 Vue 导入 - 关键转换
    code = code.replace(
      /import\s+Vue\s+from\s+['"]vue['\"]/g,
      'import { createApp } from \'vue\''
    )

    // require('element-ui/package.json') to require('element-plus/package.json')
    code = code.replace(
      /require\(\s*['"]element-ui\/package\.json['\"]\s*\)/g,
      'require(\'element-plus/package.json\')'
    )

    // 替换 Vue 2 的全局 API
    // code = code.replace(/Vue\.extend\(/g, 'defineComponent(')
    // code = code.replace(/Vue\.component\(/g, 'app.component(')
    // code = code.replace(/Vue\.use\(/g, 'app.use(')
    // code = code.replace(/Vue\.config\./g, 'app.config.')
    // code = code.replace(/Vue\.mixin\(/g, 'app.mixin(')
    // code = code.replace(/Vue\.directive\(/g, 'app.directive(')
    // code = code.replace(/Vue\.filter\(/g, 'app.config.globalProperties.$filters = app.config.globalProperties.$filters || {}; app.config.globalProperties.$filters[')
    //
    // // 替换 Element UI 使用
    // code = code.replace(/Vue\.use\(ElementUI\)/g, 'app.use(ElementPlus)')
    //
    // // 替换 new Vue() 为 createApp() - 更复杂的处理
    // // 首先处理简单的 new Vue({...}).$mount('#app') 模式
    // code = code.replace(
    //   /new\s+Vue\(\s*{([^}]+)}\s*\)\.\$mount\(['"]#app['"]\)/g,
    //   'const app = createApp({\n$1\n})\napp.mount(\'#app\')'
    // )
    //
    // // 处理更复杂的 new Vue() 模式
    // code = code.replace(
    //   /new\s+Vue\(\s*{([\s\S]*?)}\s*\)\.\$mount\(['"]#app['"]\)/g,
    //   'const app = createApp({\n$1\n})\napp.mount(\'#app\')'
    // )
    //
    // // 处理没有 $mount 的 new Vue() 模式
    // code = code.replace(
    //   /new\s+Vue\(\s*{([\s\S]*?)}\s*\)/g,
    //   'const app = createApp({\n$1\n})'
    // )
    //
    // // 特殊处理 main.js 中的 Vue 实例创建
    // // 匹配 new Vue({ router, store, render: h => h(App) }).$mount('#app')
    // code = code.replace(
    //   /new\s+Vue\(\s*{\s*router,\s*store,\s*render:\s*h\s*=>\s*h\(App\)\s*}\s*\)\.\$mount\(['"]#app['"]\)/g,
    //   'const app = createApp({\n  router,\n  store,\n  render: h => h(App)\n})\napp.mount(\'#app\')'
    // )
    //
    // // 处理 Vue.config.productionTip
    // code = code.replace(
    //   /Vue\.config\.productionTip\s*=\s*false/g,
    //   '// Vue 3 中移除了 productionTip 配置'
    // )
    //
    // // 处理 Vue.mixin
    // code = code.replace(
    //   /Vue\.mixin\(\s*{([\s\S]*?)}\s*\)/g,
    //   'app.mixin({\n$1\n})'
    // )
    //
    // // 处理 Vue.directive
    // code = code.replace(
    //   /Vue\.directive\(\s*['"]([^'"]+)['"],\s*{([\s\S]*?)}\s*\)/g,
    //   'app.directive(\'$1\', {\n$2\n})'
    // )
    //
    // // 处理 Vue.filter - 转换为全局属性
    // code = code.replace(
    //   /Vue\.filter\(\s*['"]([^'"]+)['"],\s*function\s*\(([^)]*)\)\s*{([\s\S]*?)}\s*\)/g,
    //   'app.config.globalProperties.$filters = app.config.globalProperties.$filters || {}\napp.config.globalProperties.$filters[\'$1\'] = function($2) {\n$3\n}'
    // )

    // 替换 $refs 访问方式（需要更复杂的 AST 处理）
    // 这里只做简单的字符串替换示例

    // 处理 Vue 文件中的 SCSS @import 转换为 @use
    // code = this.transformScssImports(code)

    // 注意：RenderContentTransformer 现在在 VueCodeMigrator 中调用
    // 这里不再调用 renderContentTransformer，避免重复转换

    // 修复多行 @click 事件处理器语法问题
    code = this.fixMultilineClickHandlers(code);

    return code
  }

  /**
   * 修复多行 @click 事件处理器语法问题
   * 将多行语句转换为箭头函数格式
   * @param {string} code - Vue 文件代码
   * @returns {string} 转换后的代码
   */
  fixMultilineClickHandlers(code) {
    // 匹配多行 @click 事件处理器，使用更精确的正则表达式
    // 匹配 @click=" 开始，到下一个 " 结束的内容，支持跨行
    const multilineClickRegex = /@click="([\s\S]*?)"/g;

    return code.replace(multilineClickRegex, (match, clickContent) => {
      // 清理内容，移除首尾空白字符
      const cleanContent = clickContent.trim();

      // 检查是否已经是函数格式
      // 更精确的检测：检查是否以箭头函数或function开头
      const trimmedContent = cleanContent.replace(/\s+/g, ' ').trim();
      if (trimmedContent.startsWith('()') ||
          trimmedContent.startsWith('(') && trimmedContent.includes(') =>') ||
          trimmedContent.startsWith('function')) {
        return match; // 已经是函数格式，不需要转换
      }

      // 检查是否是单行简单表达式（不包含换行符）
      if (!cleanContent.includes('\n')) {
        return match; // 单行表达式，不需要转换
      }

      // 将多行语句转换为箭头函数
      // 分割成多个语句，处理嵌套的函数调用
      const lines = cleanContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

      // 重新组装代码，保持原有的结构但确保语法正确
      let processedLines = [];
      let currentStatement = '';
      let braceCount = 0;

      for (const line of lines) {
        // 计算大括号的嵌套层级
        for (const char of line) {
          if (char === '{') braceCount++;
          if (char === '}') braceCount--;
        }

        currentStatement += (currentStatement ? ' ' : '') + line;

        // 如果大括号平衡且不是以逗号结尾，则认为是一个完整的语句
        if (braceCount === 0 && !line.endsWith(',')) {
          // 确保语句以分号结尾（除非已经有分号或是大括号结尾）
          if (!currentStatement.endsWith(';') && !currentStatement.endsWith('}')) {
            currentStatement += ';';
          }
          processedLines.push(currentStatement);
          currentStatement = '';
        }
      }

      // 处理剩余的语句
      if (currentStatement.trim()) {
        if (!currentStatement.endsWith(';') && !currentStatement.endsWith('}')) {
          currentStatement += ';';
        }
        processedLines.push(currentStatement);
      }

      // 构建箭头函数
      const functionBody = processedLines.join(' ');
      return `@click="() => { ${functionBody} }"`;
    });
  }

  /**
   * 转换 SCSS 中的 @import 为 @use
   * @param {string} code - Vue 文件代码
   * @returns {string} 转换后的代码
   */
  transformScssImports(code) {
    // 匹配 <style lang="scss" scoped> 或 <style lang="scss"> 块
    const styleRegex = /<style\s+lang=["']scss["'](?:\s+scoped)?\s*>([\s\S]*?)<\/style>/gi

    return code.replace(styleRegex, (match, styleContent) => {
      let transformedStyle = styleContent

      // 转换 @import 为 @use，但需要智能判断转换方式
      transformedStyle = transformedStyle.replace(
        /@import\s+['"]([^'"]+)['"];?/g,
        (importMatch, path) => {
          // 如果是外部 URL（http/https）或 .css 文件，保持 @import
          if (path.match(/^https?:/) || path.endsWith('.css')) {
            return importMatch // 保持原样
          }

          // 根据路径类型决定转换方式
          const filename = path.split('/').pop().replace(/^_/, '') // 移除前缀下划线

          // 对于常见的基础文件（变量、混合器等），使用 as * 以保持兼容性
          const baseFiles = ['variables', 'mixins', 'functions', 'utils', 'helpers', 'base', 'reset', 'normalize']
          const isBaseFile = baseFiles.some(base => filename.startsWith(base))

          if (isBaseFile) {
            return `@use '${path}' as *;`
          } else {
            // 对于组件或其他模块，使用命名空间
            const namespace = filename.replace(/\.(scss|sass)$/, '')
            return `@use '${path}' as ${namespace};`
          }
        }
      )

      // 返回更新后的 style 标签
      return match.replace(styleContent, transformedStyle)
    })
  }
}

module.exports = CustomVueTransformer
