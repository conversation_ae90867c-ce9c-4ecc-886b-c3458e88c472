<template>
  <div>
    <el-tree :render-content="renderContent" />
  </div>
</template>

<script>
export default {
  methods: {
    renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      if (data.id === '-1') {
        return (
          <div class="custom-tree-node">
            {data.title}{' '}
            <span class="connect_id_number" style="opacity:0">
              {data.id}
            </span>
            <span>
              <el-button
                type="text"
                size="mini"
                on-click={() => this.expandAll(false)}
              >
                全部折叠
              </el-button>
              <el-button
                type="text"
                size="mini"
                on-click={() => this.expandAll(true)}
              >
                全部展开
              </el-button>
            </span>
          </div>
        )
      } else {
        return (
          <div class="connect_id_title">
            {data.title}{' '}
            <span class="connect_id_number" style="opacity:0">
              {data.id}
            </span>
          </div>
        )
      }
    },
    
    expandAll(expand) {
      // 展开/折叠逻辑
    }
  }
}
</script>
